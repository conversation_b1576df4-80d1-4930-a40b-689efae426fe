apiVersion: apps/v1
kind: Deployment
metadata:
  name: RESOURCE_NAME
  namespace: NAMESPACE
  labels:
    app: RESOURCE_NAME
    app.kubernetes.io/name: RESOURCE_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: RESOURCE_NAME
    environment: ENVIRONMENT
    deployment.strategy: rolling-update
    # Display name for human-readable identification
    app.display-name: "APP_NAME"
  annotations:
    deployment.kubernetes.io/revision: "1"
    argocd.argoproj.io/sync-wave: "2"
spec:
  replicas: REPLICAS_DEFAULT
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: ROLLING_UPDATE_MAX_UNAVAILABLE
      maxSurge: ROLLING_UPDATE_MAX_SURGE
  progressDeadlineSeconds: ROLLING_UPDATE_PROGRESS_DEADLINE
  revisionHistoryLimit: ROLLING_UPDATE_REVISION_HISTORY
  selector:
    matchLabels:
      app: RESOURCE_NAME
      app.kubernetes.io/name: RESOURCE_NAME
      app.kubernetes.io/version: "APP_VERSION"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: RESOURCE_NAME
        app.kubernetes.io/name: RESOURCE_NAME
        app.kubernetes.io/component: APP_TYPE
        app.kubernetes.io/version: "APP_VERSION"
        app.kubernetes.io/managed-by: argocd
        app.kubernetes.io/part-of: RESOURCE_NAME
        environment: ENVIRONMENT
        deployment.strategy: rolling-update
        # Display name for human-readable identification
        app.display-name: "APP_NAME"
    spec:
      securityContext:
        runAsNonRoot: SECURITY_RUN_AS_NON_ROOT
        runAsUser: SECURITY_RUN_AS_USER
        runAsGroup: SECURITY_RUN_AS_GROUP
        fsGroup: SECURITY_FS_GROUP
      restartPolicy: Always
      terminationGracePeriodSeconds: TERMINATION_GRACE_PERIOD
      containers:
      - name: RESOURCE_NAME
        image: CONTAINER_IMAGE
        imagePullPolicy: IMAGE_PULL_POLICY
        securityContext:
          runAsNonRoot: SECURITY_RUN_AS_NON_ROOT
          runAsUser: SECURITY_RUN_AS_USER
          runAsGroup: SECURITY_RUN_AS_GROUP
          readOnlyRootFilesystem: SECURITY_READ_ONLY_ROOT_FS
          allowPrivilegeEscalation: SECURITY_ALLOW_PRIVILEGE_ESCALATION
          capabilities:
            drop: [SECURITY_CAPABILITIES_DROP]
        ports:
        - containerPort: CONTAINER_PORT
          name: http
          protocol: TCP
        startupProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: HEALTH_CHECK_PORT
            scheme: HEALTH_CHECK_SCHEME
          initialDelaySeconds: STARTUP_PROBE_INITIAL_DELAY
          periodSeconds: STARTUP_PROBE_PERIOD
          timeoutSeconds: STARTUP_PROBE_TIMEOUT
          failureThreshold: STARTUP_PROBE_FAILURE_THRESHOLD
          successThreshold: STARTUP_PROBE_SUCCESS_THRESHOLD
        readinessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: HEALTH_CHECK_PORT
            scheme: HEALTH_CHECK_SCHEME
          initialDelaySeconds: READINESS_PROBE_INITIAL_DELAY
          periodSeconds: READINESS_PROBE_PERIOD
          timeoutSeconds: READINESS_PROBE_TIMEOUT
          failureThreshold: READINESS_PROBE_FAILURE_THRESHOLD
          successThreshold: READINESS_PROBE_SUCCESS_THRESHOLD
        livenessProbe:
          httpGet:
            path: HEALTH_CHECK_PATH
            port: HEALTH_CHECK_PORT
            scheme: HEALTH_CHECK_SCHEME
          initialDelaySeconds: LIVENESS_PROBE_INITIAL_DELAY
          periodSeconds: LIVENESS_PROBE_PERIOD
          timeoutSeconds: LIVENESS_PROBE_TIMEOUT
          failureThreshold: LIVENESS_PROBE_FAILURE_THRESHOLD
          successThreshold: LIVENESS_PROBE_SUCCESS_THRESHOLD
        env:
        - name: NODE_ENV
          value: ENVIRONMENT
        - name: PORT
          value: "CONTAINER_PORT"
        envFrom:
        - configMapRef:
            name: RESOURCE_NAME-config
        - secretRef:
            name: RESOURCE_NAME-secret
        resources:
          requests:
            memory: "MEMORY_REQUEST"
            cpu: "CPU_REQUEST"
          limits:
            memory: "MEMORY_LIMIT"
            cpu: "CPU_LIMIT"
