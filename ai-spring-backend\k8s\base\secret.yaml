apiVersion: v1
kind: Secret
metadata:
  name: ai-spring-backend-secret
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "AI Spring Backend"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
type: Opaque
data:
  # Authentication Secrets (Base64 encoded)
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=

  # SMTP Secrets
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==

  # OAuth Secrets
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3VpbG9yb3IzZm0xanFjNDkzb3MuYXBwcy5nb29nbGV1c2VyY29udGVudC5jb20=
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSUk1U3o1Z3pCczI5OEFBYlQ=
