apiVersion: v1
kind: Secret
metadata:
  name: ai-spring-backend-secret
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "AI Spring Backend"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
type: Opaque
data:
  # Authentication Secrets (Base64 encoded)
  JWT_SECRET: JWT_SECRET_B64
  
  # Database Secrets
  DB_PASSWORD: DB_PASSWORD_B64
  
  # SMTP Secrets
  SMTP_USER: SMTP_USER_B64
  SMTP_PASS: SMTP_PASS_B64
  
  # OAuth Secrets
  GOOGLE_CLIENT_ID: GOOGLE_CLIENT_ID_B64
  GOOGLE_CLIENT_SECRET: GOOGLE_CLIENT_SECRET_B64
