apiVersion: v1
kind: Service
metadata:
  name: ai-spring-backend-service
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    # Display name for human-readable identification
    app.display-name: "AI Spring Backend"
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  type: LoadBalancer
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: ai-spring-backend
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/managed-by: argocd
